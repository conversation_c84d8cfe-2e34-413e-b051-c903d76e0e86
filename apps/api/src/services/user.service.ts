import { prisma } from '@/lib/db';
import { 
    Result, 
    User, 
    CreateUser, 
    UpdateUser,
    UserNotificationPreferences,
    CreateUserNotificationPreferences,
    ApplicationUserSubscription,
    UserSubscription,
    SubscriptionUpdate,
    PaginatedResult,
    UserFilter
} from '@/types';
import { BaseService } from './base.service';
import { createId } from '@paralleldrive/cuid2';

/**
 * Service for managing users, their preferences, and subscriptions
 */
export class UserService extends BaseService {
    constructor() {
        super('User');
    }

    /**
     * Create a new user
     */
    async createUser(data: CreateUser): Promise<Result<User>> {
        try {
            const preferences = data.preferences ? JSON.stringify(data.preferences) : '{}';
            
            const user = await prisma.user.create({
                data: {
                    ...data,
                    preferences,
                    novuSubscriberId: data.novuSubscriberId || createId()
                }
            });

            return this.created(user);
        } catch (error) {
            if (error.code === 'P2002' && error.meta?.target?.includes('email')) {
                return this.badRequest('User with this email already exists');
            }
            console.error('Error creating user:', error);
            return this.serverError();
        }
    }

    /**
     * Get user by ID
     */
    async getUser(userId: string): Promise<Result<User>> {
        try {
            const user = await prisma.user.findUnique({
                where: { id: userId },
                include: {
                    notificationPreferences: true,
                    applicationSubscriptions: {
                        include: {
                            app: {
                                select: { appId: true, name: true, description: true }
                            }
                        }
                    }
                }
            });

            if (!user) {
                return this.notFound();
            }

            return this.success(user);
        } catch (error) {
            console.error('Error fetching user:', error);
            return this.serverError();
        }
    }

    /**
     * Get user by email
     */
    async getUserByEmail(email: string): Promise<Result<User>> {
        try {
            const user = await prisma.user.findUnique({
                where: { email },
                include: {
                    notificationPreferences: true,
                    applicationSubscriptions: {
                        include: {
                            app: {
                                select: { appId: true, name: true, description: true }
                            }
                        }
                    }
                }
            });

            if (!user) {
                return this.notFound();
            }

            return this.success(user);
        } catch (error) {
            console.error('Error fetching user by email:', error);
            return this.serverError();
        }
    }

    /**
     * Update user
     */
    async updateUser(userId: string, data: UpdateUser): Promise<Result<User>> {
        try {
            const updateData: any = { ...data };
            if (data.preferences) {
                updateData.preferences = JSON.stringify(data.preferences);
            }

            const user = await prisma.user.update({
                where: { id: userId },
                data: updateData,
                include: {
                    notificationPreferences: true,
                    applicationSubscriptions: {
                        include: {
                            app: {
                                select: { appId: true, name: true, description: true }
                            }
                        }
                    }
                }
            });

            return this.success(user);
        } catch (error) {
            if (error.code === 'P2002' && error.meta?.target?.includes('email')) {
                return this.badRequest('User with this email already exists');
            }
            console.error('Error updating user:', error);
            return this.serverError();
        }
    }

    /**
     * Get users with filtering and pagination
     */
    async getUsers(filter: UserFilter = {}): Promise<Result<PaginatedResult<User>>> {
        try {
            const {
                page = 1,
                limit = 20,
                email,
                name,
                appId,
                createdAfter,
                createdBefore
            } = filter;

            const skip = (page - 1) * limit;
            const where: any = {};

            if (email) {
                where.email = { contains: email, mode: 'insensitive' };
            }
            if (name) {
                where.name = { contains: name, mode: 'insensitive' };
            }
            if (appId) {
                where.applicationSubscriptions = {
                    some: { appId }
                };
            }
            if (createdAfter || createdBefore) {
                where.createdAt = {};
                if (createdAfter) where.createdAt.gte = new Date(createdAfter);
                if (createdBefore) where.createdAt.lte = new Date(createdBefore);
            }

            const [users, total] = await Promise.all([
                prisma.user.findMany({
                    where,
                    skip,
                    take: limit,
                    orderBy: { createdAt: 'desc' },
                    include: {
                        notificationPreferences: true,
                        applicationSubscriptions: {
                            include: {
                                app: {
                                    select: { appId: true, name: true }
                                }
                            }
                        }
                    }
                }),
                prisma.user.count({ where })
            ]);

            const result: PaginatedResult<User> = {
                data: users,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages: Math.ceil(total / limit)
                }
            };

            return this.success(result);
        } catch (error) {
            console.error('Error fetching users:', error);
            return this.serverError();
        }
    }

    /**
     * Delete user
     */
    async deleteUser(userId: string): Promise<Result<null>> {
        try {
            await prisma.user.delete({
                where: { id: userId }
            });

            return this.success(null);
        } catch (error) {
            console.error('Error deleting user:', error);
            return this.serverError();
        }
    }

    // ============ NOTIFICATION PREFERENCES ============

    /**
     * Set user notification preferences for a specific notification type
     */
    async setNotificationPreferences(
        userId: string,
        notificationType: string,
        preferences: CreateUserNotificationPreferences
    ): Promise<Result<UserNotificationPreferences>> {
        try {
            // Verify user exists
            const userExists = await prisma.user.findUnique({
                where: { id: userId },
                select: { id: true }
            });

            if (!userExists) {
                return this.badRequest('User not found');
            }

            const userPrefs = await prisma.userNotificationPreferences.upsert({
                where: {
                    userId_notificationType: {
                        userId,
                        notificationType
                    }
                },
                update: preferences,
                create: {
                    id: createId(),
                    userId,
                    notificationType,
                    ...preferences
                }
            });

            return this.success(userPrefs);
        } catch (error) {
            console.error('Error setting notification preferences:', error);
            return this.serverError();
        }
    }

    /**
     * Get all notification preferences for a user
     */
    async getUserNotificationPreferences(userId: string): Promise<Result<UserNotificationPreferences[]>> {
        try {
            const preferences = await prisma.userNotificationPreferences.findMany({
                where: { userId },
                orderBy: { notificationType: 'asc' }
            });

            return this.success(preferences);
        } catch (error) {
            console.error('Error fetching user notification preferences:', error);
            return this.serverError();
        }
    }

    /**
     * Get notification preferences for a specific type
     */
    async getNotificationPreferences(
        userId: string,
        notificationType: string
    ): Promise<Result<UserNotificationPreferences | null>> {
        try {
            const preferences = await prisma.userNotificationPreferences.findUnique({
                where: {
                    userId_notificationType: {
                        userId,
                        notificationType
                    }
                }
            });

            return this.success(preferences);
        } catch (error) {
            console.error('Error fetching notification preferences:', error);
            return this.serverError();
        }
    }

    /**
     * Update notification preferences
     */
    async updateNotificationPreferences(
        userId: string,
        notificationType: string,
        updates: Partial<CreateUserNotificationPreferences>
    ): Promise<Result<UserNotificationPreferences>> {
        try {
            const preferences = await prisma.userNotificationPreferences.update({
                where: {
                    userId_notificationType: {
                        userId,
                        notificationType
                    }
                },
                data: updates
            });

            return this.success(preferences);
        } catch (error) {
            console.error('Error updating notification preferences:', error);
            return this.serverError();
        }
    }

    /**
     * Delete notification preferences
     */
    async deleteNotificationPreferences(
        userId: string,
        notificationType: string
    ): Promise<Result<null>> {
        try {
            await prisma.userNotificationPreferences.delete({
                where: {
                    userId_notificationType: {
                        userId,
                        notificationType
                    }
                }
            });

            return this.success(null);
        } catch (error) {
            console.error('Error deleting notification preferences:', error);
            return this.serverError();
        }
    }

    /**
     * Set global notification preferences (applies to all notification types)
     */
    async setGlobalNotificationPreferences(
        userId: string,
        preferences: CreateUserNotificationPreferences
    ): Promise<Result<UserNotificationPreferences[]>> {
        try {
            // Get all existing notification types for this user
            const existingPrefs = await prisma.userNotificationPreferences.findMany({
                where: { userId },
                select: { notificationType: true }
            });

            const notificationTypes = existingPrefs.map(p => p.notificationType);
            
            // If no existing preferences, create a default set
            if (notificationTypes.length === 0) {
                notificationTypes.push('INFO', 'SUCCESS', 'WARNING', 'ERROR', 'REMINDER');
            }

            const results: UserNotificationPreferences[] = [];

            // Update preferences for each notification type
            for (const notificationType of notificationTypes) {
                const result = await this.setNotificationPreferences(userId, notificationType, preferences);
                if (result.success) {
                    results.push(result.data);
                }
            }

            return this.success(results);
        } catch (error) {
            console.error('Error setting global notification preferences:', error);
            return this.serverError();
        }
    }

    // ============ APPLICATION SUBSCRIPTIONS ============

    /**
     * Subscribe user to an application
     */
    async subscribeToApplication(
        userId: string,
        appId: string,
        preferences?: any
    ): Promise<Result<ApplicationUserSubscription>> {
        try {
            // Verify user and app exist
            const [user, app] = await Promise.all([
                prisma.user.findUnique({ where: { id: userId }, select: { id: true } }),
                prisma.application.findUnique({ where: { appId }, select: { appId: true } })
            ]);

            if (!user) {
                return this.badRequest('User not found');
            }
            if (!app) {
                return this.badRequest('Application not found');
            }

            const subscription = await prisma.applicationUserSubscription.upsert({
                where: {
                    userId_appId: {
                        userId,
                        appId
                    }
                },
                update: {
                    preferences: preferences ? JSON.stringify(preferences) : undefined
                },
                create: {
                    id: createId(),
                    userId,
                    appId,
                    novuSubscriberId: createId(),
                    preferences: JSON.stringify(preferences || {})
                },
                include: {
                    app: {
                        select: { appId: true, name: true, description: true }
                    }
                }
            });

            return this.success(subscription);
        } catch (error) {
            console.error('Error subscribing to application:', error);
            return this.serverError();
        }
    }

    /**
     * Unsubscribe user from an application
     */
    async unsubscribeFromApplication(userId: string, appId: string): Promise<Result<null>> {
        try {
            await prisma.applicationUserSubscription.delete({
                where: {
                    userId_appId: {
                        userId,
                        appId
                    }
                }
            });

            return this.success(null);
        } catch (error) {
            console.error('Error unsubscribing from application:', error);
            return this.serverError();
        }
    }

    /**
     * Get user's application subscriptions
     */
    async getUserSubscriptions(userId: string): Promise<Result<ApplicationUserSubscription[]>> {
        try {
            const subscriptions = await prisma.applicationUserSubscription.findMany({
                where: { userId },
                include: {
                    app: {
                        select: { appId: true, name: true, description: true, isActive: true }
                    }
                },
                orderBy: { createdAt: 'desc' }
            });

            return this.success(subscriptions);
        } catch (error) {
            console.error('Error fetching user subscriptions:', error);
            return this.serverError();
        }
    }

    /**
     * Update application subscription preferences
     */
    async updateSubscriptionPreferences(
        userId: string,
        appId: string,
        preferences: any
    ): Promise<Result<ApplicationUserSubscription>> {
        try {
            const subscription = await prisma.applicationUserSubscription.update({
                where: {
                    userId_appId: {
                        userId,
                        appId
                    }
                },
                data: {
                    preferences: JSON.stringify(preferences)
                },
                include: {
                    app: {
                        select: { appId: true, name: true, description: true }
                    }
                }
            });

            return this.success(subscription);
        } catch (error) {
            console.error('Error updating subscription preferences:', error);
            return this.serverError();
        }
    }

    /**
     * Get application subscribers
     */
    async getApplicationSubscribers(
        appId: string,
        filter: { page?: number; limit?: number } = {}
    ): Promise<Result<PaginatedResult<ApplicationUserSubscription>>> {
        try {
            const { page = 1, limit = 50 } = filter;
            const skip = (page - 1) * limit;

            const [subscriptions, total] = await Promise.all([
                prisma.applicationUserSubscription.findMany({
                    where: { appId },
                    skip,
                    take: limit,
                    include: {
                        user: {
                            select: { id: true, email: true, name: true, createdAt: true }
                        }
                    },
                    orderBy: { createdAt: 'desc' }
                }),
                prisma.applicationUserSubscription.count({ where: { appId } })
            ]);

            const result: PaginatedResult<ApplicationUserSubscription> = {
                data: subscriptions,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages: Math.ceil(total / limit)
                }
            };

            return this.success(result);
        } catch (error) {
            console.error('Error fetching application subscribers:', error);
            return this.serverError();
        }
    }

    // ============ BULK OPERATIONS ============

    /**
     * Bulk create users
     */
    async createBulkUsers(users: CreateUser[]): Promise<Result<{ successful: User[]; failed: Array<{ user: CreateUser; error: string }> }>> {
        try {
            const results = {
                successful: [] as User[],
                failed: [] as Array<{ user: CreateUser; error: string }>
            };

            await prisma.$transaction(async (tx) => {
                for (const userData of users) {
                    try {
                        const preferences = userData.preferences ? JSON.stringify(userData.preferences) : '{}';
                        
                        const user = await tx.user.create({
                            data: {
                                ...userData,
                                id: createId(),
                                preferences,
                                novuSubscriberId: userData.novuSubscriberId || createId()
                            }
                        });

                        results.successful.push(user);
                    } catch (error) {
                        results.failed.push({
                            user: userData,
                            error: error instanceof Error ? error.message : 'Unknown error'
                        });
                    }
                }
            });

            return this.success(results);
        } catch (error) {
            console.error('Error creating bulk users:', error);
            return this.serverError();
        }
    }

    /**
     * Bulk subscribe users to application
     */
    async bulkSubscribeToApplication(
        userIds: string[],
        appId: string
    ): Promise<Result<{ successful: ApplicationUserSubscription[]; failed: Array<{ userId: string; error: string }> }>> {
        try {
            // Verify app exists
            const app = await prisma.application.findUnique({
                where: { appId },
                select: { appId: true }
            });

            if (!app) {
                return this.badRequest('Application not found');
            }

            const results = {
                successful: [] as ApplicationUserSubscription[],
                failed: [] as Array<{ userId: string; error: string }>
            };

            await prisma.$transaction(async (tx) => {
                for (const userId of userIds) {
                    try {
                        const subscription = await tx.applicationUserSubscription.upsert({
                            where: {
                                userId_appId: {
                                    userId,
                                    appId
                                }
                            },
                            update: {},
                            create: {
                                id: createId(),
                                userId,
                                appId,
                                novuSubscriberId: createId(),
                                preferences: '{}'
                            },
                            include: {
                                user: {
                                    select: { id: true, email: true, name: true }
                                }
                            }
                        });

                        results.successful.push(subscription);
                    } catch (error) {
                        results.failed.push({
                            userId,
                            error: error instanceof Error ? error.message : 'Unknown error'
                        });
                    }
                }
            });

            return this.success(results);
        } catch (error) {
            console.error('Error bulk subscribing to application:', error);
            return this.serverError();
        }
    }

    // ============ UTILITY METHODS ============

    /**
     * Check if user exists
     */
    async userExists(userId: string): Promise<boolean> {
        try {
            const user = await prisma.user.findUnique({
                where: { id: userId },
                select: { id: true }
            });

            return !!user;
        } catch (error) {
            console.error('Error checking if user exists:', error);
            return false;
        }
    }

    /**
     * Get user notification summary
     */
    async getUserNotificationSummary(userId: string): Promise<Result<{
        totalNotifications: number;
        unreadNotifications: number;
        notificationsByType: Record<string, number>;
        recentNotifications: any[];
    }>> {
        try {
            const [
                total,
                unread,
                byType,
                recent
            ] = await Promise.all([
                prisma.notification.count({
                    where: { recipientId: userId }
                }),
                prisma.notification.count({
                    where: { recipientId: userId, readAt: null }
                }),
                prisma.notification.groupBy({
                    by: ['type'],
                    where: { recipientId: userId },
                    _count: { type: true }
                }),
                prisma.notification.findMany({
                    where: { recipientId: userId },
                    take: 10,
                    orderBy: { createdAt: 'desc' },
                    select: {
                        id: true,
                        title: true,
                        type: true,
                        priority: true,
                        createdAt: true,
                        readAt: true,
                        app: {
                            select: { name: true }
                        }
                    }
                })
            ]);

            const summary = {
                totalNotifications: total,
                unreadNotifications: unread,
                notificationsByType: byType.reduce((acc, item) => {
                    acc[item.type] = item._count.type;
                    return acc;
                }, {} as Record<string, number>),
                recentNotifications: recent
            };

            return this.success(summary);
        } catch (error) {
            console.error('Error fetching user notification summary:', error);
            return this.serverError();
        }
    }

    /**
     * Get or create user by email (useful for external integrations)
     */
    async getOrCreateUser(email: string, userData?: Partial<CreateUser>): Promise<Result<User>> {
        try {
            // Try to find existing user
            const existingUser = await prisma.user.findUnique({
                where: { email }
            });

            if (existingUser) {
                return this.success(existingUser);
            }

            // Create new user
            const newUserData: CreateUser = {
                email,
                name: userData?.name,
                avatar: userData?.avatar,
                phone: userData?.phone,
                preferences: userData?.preferences,
                novuSubscriberId: userData?.novuSubscriberId
            };

            return await this.createUser(newUserData);
        } catch (error) {
            console.error('Error getting or creating user:', error);
            return this.serverError();
        }
    }
}

export const userService = new UserService();