import { 
    Result, 
    NotificationChannel, 
    NotificationProvider,
    SendNotificationRequest,
    SendNotificationResponse,
    NovuNotificationPayload,
    NovuSubscriberData,
    ResendEmailPayload,
    TwilioSmsPayload,
    Application,
    CreateNotification,
    DeliveryStatusType
} from '@/types';
import { BaseService } from './base.service';
import { applicationService } from './application.service';
import { notificationService } from './dbNotification.service';
import { createId } from '@paralleldrive/cuid2';
import { prisma } from '@/lib/db';

/**
 * Provider Service - Handles integration with multiple notification providers
 */
export class ProviderService extends BaseService {
    private novuApiUrl = 'https://api.novu.co/v1';
    private resendApiUrl = 'https://api.resend.com';
    private twilioApiUrl = 'https://api.twilio.com/2010-04-01';

    constructor() {
        super('Provider');
    }

    /**
     * Send notification through the appropriate providers based on application configuration
     */
    async sendNotification(request: SendNotificationRequest): Promise<Result<SendNotificationResponse>> {
        try {
            const { notification, channels, template, providerOverride, retryConfig } = request;

            // Get application configuration
            const appResult = await applicationService.getApplication(notification.appId);
            if (!appResult.success) {
                return this.badRequest('Application not found');
            }

            const app = appResult.data;
            const response: SendNotificationResponse = {
                success: false,
                notificationId: notification.id || createId(),
                deliveries: [],
                errors: []
            };

            // Create notification record
            const notificationResult = await notificationService.createNotification({
                ...notification,
                id: response.notificationId
            });

            if (!notificationResult.success) {
                return this.serverError('Failed to create notification record');
            }

            // Send through each requested channel
            for (const channel of channels) {
                try {
                    // Check user preferences first
                    const shouldSend = await notificationService.shouldSendNotification(
                        notification.recipientId,
                        notification.type,
                        channel
                    );

                    if (!shouldSend) {
                        response.deliveries.push({
                            channel,
                            provider: 'system',
                            status: 'BLOCKED',
                            errorMessage: 'User preferences block this notification'
                        });
                        continue;
                    }

                    // Determine provider for this channel
                    const provider = providerOverride || this.selectProvider(app, channel);
                    
                    // Send through the selected provider
                    const deliveryResult = await this.sendThroughProvider(
                        app,
                        notification,
                        channel,
                        provider,
                        template
                    );

                    if (deliveryResult.success) {
                        response.deliveries.push({
                            channel,
                            provider,
                            status: 'SENT',
                            providerMessageId: deliveryResult.data.messageId
                        });

                        // Update delivery status
                        await notificationService.updateDeliveryStatus(
                            response.notificationId,
                            channel,
                            provider,
                            'SENT',
                            {
                                providerMessageId: deliveryResult.data.messageId,
                                providerResponse: deliveryResult.data
                            }
                        );
                    } else {
                        // Try fallback if available and strategy permits
                        const fallbackResult = await this.handleFallback(
                            app,
                            notification,
                            channel,
                            provider,
                            deliveryResult.message || 'Primary provider failed',
                            template
                        );

                        if (fallbackResult.success) {
                            response.deliveries.push({
                                channel,
                                provider: fallbackResult.data.provider,
                                status: 'SENT',
                                providerMessageId: fallbackResult.data.messageId
                            });
                        } else {
                            response.deliveries.push({
                                channel,
                                provider,
                                status: 'FAILED',
                                errorMessage: deliveryResult.message
                            });

                            response.errors?.push({
                                channel,
                                provider,
                                error: deliveryResult.message || 'Unknown error'
                            });

                            // Update delivery status as failed
                            await notificationService.updateDeliveryStatus(
                                response.notificationId,
                                channel,
                                provider,
                                'FAILED',
                                {
                                    errorMessage: deliveryResult.message,
                                    providerResponse: deliveryResult.data
                                }
                            );
                        }
                    }
                } catch (error) {
                    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                    response.errors?.push({
                        channel,
                        provider: 'unknown',
                        error: errorMessage
                    });

                    response.deliveries.push({
                        channel,
                        provider: 'unknown',
                        status: 'FAILED',
                        errorMessage
                    });
                }
            }

            // Determine overall success
            response.success = response.deliveries.some(d => d.status === 'SENT');

            // Update notification status
            const finalStatus = response.success ? 'SENT' : 'FAILED';
            await notificationService.updateNotification(response.notificationId, {
                status: finalStatus
            });

            return this.success(response);
        } catch (error) {
            console.error('Error sending notification:', error);
            return this.serverError();
        }
    }

    /**
     * Send notification through a specific provider
     */
    private async sendThroughProvider(
        app: Application,
        notification: CreateNotification,
        channel: NotificationChannel,
        provider: string,
        template?: string
    ): Promise<Result<{ messageId: string; response: any }>> {
        try {
            switch (provider.toLowerCase()) {
                case 'novu':
                    return await this.sendThroughNovu(app, notification, channel, template);
                
                case 'resend':
                    if (channel === 'email') {
                        return await this.sendThroughResend(app, notification);
                    }
                    return this.badRequest('Resend only supports email notifications');
                
                case 'twilio':
                    if (channel === 'sms') {
                        return await this.sendThroughTwilio(app, notification);
                    }
                    return this.badRequest('Twilio SMS only supports SMS notifications');
                
                case 'direct':
                    return await this.sendDirect(app, notification, channel);
                
                default:
                    return this.badRequest(`Unknown provider: ${provider}`);
            }
        } catch (error) {
            console.error(`Error sending through ${provider}:`, error);
            return this.serverError();
        }
    }

    /**
     * Send notification through Novu
     */
    private async sendThroughNovu(
        app: Application,
        notification: CreateNotification,
        channel: NotificationChannel,
        template?: string
    ): Promise<Result<{ messageId: string; response: any }>> {
        try {
            if (!app.novuApiKey) {
                return this.badRequest('Novu API key not configured');
            }

            // Get recipient data
            const recipient = await this.getRecipientData(notification.recipientId);
            if (!recipient.success) {
                return recipient as any;
            }

            const user = recipient.data;

            // Prepare Novu payload
            const payload: NovuNotificationPayload = {
                name: template || 'default-notification',
                to: {
                    subscriberId: notification.recipientId,
                    email: user.email,
                    firstName: user.name?.split(' ')[0],
                    lastName: user.name?.split(' ').slice(1).join(' '),
                    phone: user.phone
                },
                payload: {
                    title: notification.title,
                    message: notification.message,
                    type: notification.type,
                    priority: notification.priority,
                    ...JSON.parse(notification.metadata || '{}')
                }
            };

            // Send to Novu
            const response = await fetch(`${this.novuApiUrl}/events/trigger`, {
                method: 'POST',
                headers: {
                    'Authorization': `ApiKey ${app.novuApiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                const error = await response.text();
                return this.badRequest(`Novu API error: ${error}`);
            }

            const result = await response.json();
            
            return this.success({
                messageId: result.data.transactionId,
                response: result
            });
        } catch (error) {
            console.error('Error sending through Novu:', error);
            return this.serverError();
        }
    }

    /**
     * Send email through Resend
     */
    private async sendThroughResend(
        app: Application,
        notification: CreateNotification
    ): Promise<Result<{ messageId: string; response: any }>> {
        try {
            if (!app.resendApiKey) {
                return this.badRequest('Resend API key not configured');
            }

            // Get recipient data
            const recipient = await this.getRecipientData(notification.recipientId);
            if (!recipient.success) {
                return recipient as any;
            }

            const user = recipient.data;

            // Prepare Resend payload
            const payload: ResendEmailPayload = {
                from: '<EMAIL>', // Should be configurable
                to: user.email,
                subject: notification.title,
                html: this.formatEmailHtml(notification),
                text: notification.message,
                tags: [
                    { name: 'app_id', value: notification.appId },
                    { name: 'notification_type', value: notification.type },
                    { name: 'priority', value: notification.priority || 'NORMAL' }
                ]
            };

            // Send through Resend
            const response = await fetch(`${this.resendApiUrl}/emails`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${app.resendApiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                const error = await response.text();
                return this.badRequest(`Resend API error: ${error}`);
            }

            const result = await response.json();
            
            return this.success({
                messageId: result.id,
                response: result
            });
        } catch (error) {
            console.error('Error sending through Resend:', error);
            return this.serverError();
        }
    }

    /**
     * Send SMS through Twilio
     */
    private async sendThroughTwilio(
        app: Application,
        notification: CreateNotification
    ): Promise<Result<{ messageId: string; response: any }>> {
        try {
            if (!app.twilioAccountSid || !app.twilioAuthToken) {
                return this.badRequest('Twilio credentials not configured');
            }

            // Get recipient data
            const recipient = await this.getRecipientData(notification.recipientId);
            if (!recipient.success) {
                return recipient as any;
            }

            const user = recipient.data;

            if (!user.phone) {
                return this.badRequest('Recipient phone number not available');
            }

            // Prepare Twilio payload
            const payload: TwilioSmsPayload = {
                from: '+**********', // Should be configurable
                to: user.phone,
                body: `${notification.title}\n\n${notification.message}`
            };

            // Send through Twilio
            const auth = Buffer.from(`${app.twilioAccountSid}:${app.twilioAuthToken}`).toString('base64');
            
            const response = await fetch(
                `${this.twilioApiUrl}/Accounts/${app.twilioAccountSid}/Messages.json`,
                {
                    method: 'POST',
                    headers: {
                        'Authorization': `Basic ${auth}`,
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: new URLSearchParams(payload as any)
                }
            );

            if (!response.ok) {
                const error = await response.text();
                return this.badRequest(`Twilio API error: ${error}`);
            }

            const result = await response.json();
            
            return this.success({
                messageId: result.sid,
                response: result
            });
        } catch (error) {
            console.error('Error sending through Twilio:', error);
            return this.serverError();
        }
    }

    /**
     * Send notification directly (custom implementation)
     */
    private async sendDirect(
        app: Application,
        notification: CreateNotification,
        channel: NotificationChannel
    ): Promise<Result<{ messageId: string; response: any }>> {
        try {
            // This would implement direct delivery for specific channels
            // For now, we'll just mark as sent with a direct identifier
            const messageId = `direct_${createId()}`;
            
            // Here you would implement your custom delivery logic
            // For example, directly interfacing with email services, push notification services, etc.
            
            return this.success({
                messageId,
                response: { channel, provider: 'direct', status: 'sent' }
            });
        } catch (error) {
            console.error('Error sending direct:', error);
            return this.serverError();
        }
    }

    /**
     * Handle fallback provider logic
     */
    private async handleFallback(
        app: Application,
        notification: CreateNotification,
        channel: NotificationChannel,
        failedProvider: string,
        error: string,
        template?: string
    ): Promise<Result<{ messageId: string; provider: string }>> {
        try {
            if (!app.fallbackStrategy || app.fallbackStrategy === 'NONE') {
                return this.badRequest('No fallback strategy configured');
            }

            // Determine fallback provider
            const fallbackProvider = this.getFallbackProvider(app, channel, failedProvider);
            
            if (!fallbackProvider) {
                return this.badRequest('No fallback provider available');
            }

            // Attempt delivery through fallback
            const fallbackResult = await this.sendThroughProvider(
                app,
                notification,
                channel,
                fallbackProvider,
                template
            );

            if (fallbackResult.success) {
                // Update delivery status for fallback
                await notificationService.updateDeliveryStatus(
                    notification.id!,
                    channel,
                    fallbackProvider,
                    'SENT',
                    {
                        providerMessageId: fallbackResult.data.messageId,
                        providerResponse: fallbackResult.data
                    }
                );

                return this.success({
                    messageId: fallbackResult.data.messageId,
                    provider: fallbackProvider
                });
            }

            return this.badRequest(`Fallback also failed: ${fallbackResult.message}`);
        } catch (error) {
            console.error('Error in fallback handling:', error);
            return this.serverError();
        }
    }

    /**
     * Select the appropriate provider for a channel
     */
    private selectProvider(app: Application, channel: NotificationChannel): string {
        // Default provider selection logic
        switch (app.defaultProvider) {
            case 'NOVU':
                return 'novu';
            case 'DIRECT':
                return this.getDirectProviderForChannel(channel);
            case 'HYBRID':
                // Use Novu for in-app/push, direct providers for email/sms
                return channel === 'email' || channel === 'sms' 
                    ? this.getDirectProviderForChannel(channel)
                    : 'novu';
            default:
                return 'novu';
        }
    }

    /**
     * Get direct provider for specific channel
     */
    private getDirectProviderForChannel(channel: NotificationChannel): string {
        switch (channel) {
            case 'email':
                return 'resend';
            case 'sms':
                return 'twilio';
            default:
                return 'direct';
        }
    }

    /**
     * Get fallback provider
     */
    private getFallbackProvider(app: Application, channel: NotificationChannel, failedProvider: string): string | null {
        const providers = this.getAvailableProviders(app, channel);
        return providers.find(p => p !== failedProvider) || null;
    }

    /**
     * Get available providers for a channel
     */
    private getAvailableProviders(app: Application, channel: NotificationChannel): string[] {
        const providers: string[] = [];

        // Always include Novu if configured
        if (app.novuApiKey) {
            providers.push('novu');
        }

        // Channel-specific providers
        switch (channel) {
            case 'email':
                if (app.resendApiKey) providers.push('resend');
                break;
            case 'sms':
                if (app.twilioAccountSid && app.twilioAuthToken) providers.push('twilio');
                break;
        }

        // Always include direct as fallback
        providers.push('direct');

        return providers;
    }

    /**
     * Get recipient data
     */
    private async getRecipientData(recipientId: string): Promise<Result<{ id: string; email: string; name?: string; phone?: string }>> {
        try {
            const user = await prisma.user.findUnique({
                where: { id: recipientId },
                select: { id: true, email: true, name: true, phone: true }
            });

            if (!user) {
                return this.badRequest('Recipient not found');
            }

            return this.success(user);
        } catch (error) {
            console.error('Error fetching recipient data:', error);
            return this.serverError();
        }
    }

    /**
     * Format email HTML content
     */
    private formatEmailHtml(notification: CreateNotification): string {
        // Basic HTML template - should be more sophisticated in production
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>${notification.title}</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background-color: #f8f9fa; padding: 20px; border-radius: 5px; }
                    .content { padding: 20px 0; }
                    .footer { border-top: 1px solid #dee2e6; padding-top: 20px; font-size: 12px; color: #6c757d; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>${notification.title}</h1>
                    </div>
                    <div class="content">
                        <p>${notification.message}</p>
                    </div>
                    <div class="footer">
                        <p>This is an automated message. Please do not reply to this email.</p>
                    </div>
                </div>
            </body>
            </html>
        `;
    }

    /**
     * Handle webhook notifications from providers
     */
    async handleProviderWebhook(provider: string, payload: any): Promise<Result<null>> {
        try {
            switch (provider.toLowerCase()) {
                case 'novu':
                    return await this.handleNovuWebhook(payload);
                case 'resend':
                    return await this.handleResendWebhook(payload);
                case 'twilio':
                    return await this.handleTwilioWebhook(payload);
                default:
                    return this.badRequest(`Unknown provider webhook: ${provider}`);
            }
        } catch (error) {
            console.error(`Error handling ${provider} webhook:`, error);
            return this.serverError();
        }
    }

    /**
     * Handle Novu webhook
     */
    private async handleNovuWebhook(payload: any): Promise<Result<null>> {
        try {
            // Process Novu webhook payload
            // Update delivery statuses based on webhook events
            return this.success(null);
        } catch (error) {
            console.error('Error handling Novu webhook:', error);
            return this.serverError();
        }
    }

    /**
     * Handle Resend webhook
     */
    private async handleResendWebhook(payload: any): Promise<Result<null>> {
        try {
            // Process Resend webhook payload
            // Update delivery statuses based on webhook events
            return this.success(null);
        } catch (error) {
            console.error('Error handling Resend webhook:', error);
            return this.serverError();
        }
    }

    /**
     * Handle Twilio webhook
     */
    private async handleTwilioWebhook(payload: any): Promise<Result<null>> {
        try {
            // Process Twilio webhook payload
            // Update delivery statuses based on webhook events
            return this.success(null);
        } catch (error) {
            console.error('Error handling Twilio webhook:', error);
            return this.serverError();
        }
    }
}

export const providerService = new ProviderService();