// user.types.ts

export interface User {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  phone?: string;
  preferences: string | any;
  novuSubscriberId?: string;
  createdAt: Date;
  updatedAt: Date;
  
  // Relations (when included)
  notifications?: Notification[];
  notificationPreferences?: UserNotificationPreferences[];
  applicationSubscriptions?: ApplicationUserSubscription[];
}

export interface CreateUser {
  email: string;
  name?: string;
  avatar?: string;
  phone?: string;
  preferences?: any;
  novuSubscriberId?: string;
}

export interface UpdateUser {
  email?: string;
  name?: string;
  avatar?: string;
  phone?: string;
  preferences?: any;
  novuSubscriberId?: string;
}

export interface UserFilter {
  page?: number;
  limit?: number;
  email?: string;
  name?: string;
  appId?: string;
  createdAfter?: string | Date;
  createdBefore?: string | Date;
}

// ============ APPLICATION USER SUBSCRIPTION ============

export interface ApplicationUserSubscription {
  id: string;
  userId: string;
  appId: string;
  novuSubscriberId?: string;
  preferences: string | any;
  createdAt: Date;
  updatedAt: Date;
  
  // Relations (when included)
  user?: {
    id: string;
    email: string;
    name?: string;
    createdAt?: Date;
  };
  app?: {
    appId: string;
    name: string;
    description?: string;
    isActive?: boolean;
  };
}

// ============ CONSOLIDATED TYPES FROM EXISTING SERVICES ============

export interface Result<T = any> {
  success: boolean;
  data: T;
  message?: string;
  statusCode?: number;
}

// Re-export commonly used types for convenience
export type {
  Notification,
  CreateNotification,
  UpdateNotification,
  NotificationFilter,
  PaginatedResult,
  NotificationStats,
  BulkNotificationResult,
  UserNotificationPreferences,
  CreateUserNotificationPreferences,
  NotificationTemplate,
  CreateNotificationTemplate,
  UpdateNotificationTemplate,
  WebhookEndpoint,
  CreateWebhookEndpoint,
  WebhookDelivery,
  DeliveryStatus,
  NotificationChannel,
  NotificationStatus,
  NotificationType,
  NotificationPriority,
  DeliveryStatusType
} from './notification.types';

export type {
  Application,
  CreateApplication,
  ApplicationConfig,
  AppRegistration,
  CreateAppRegistration,
  AppApiKey,
  CreateAppApiKey,
  AppApiKeyStatus,
  ApplicationNotificationProviders,
  ApplicationEnum
} from './application.types';

// ============ ADDITIONAL USER-SPECIFIC TYPES ============

export interface UserProfile {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  phone?: string;
  timezone?: string;
  language?: string;
  metadata?: Record<string, any>;
  lastActiveAt?: Date;
  emailVerified: boolean;
  phoneVerified: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserActivity {
  id: string;
  userId: string;
  action: string;
  resource: string;
  resourceId: string;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
}

export interface UserSession {
  id: string;
  userId: string;
  token: string;
  refreshToken: string;
  expiresAt: Date;
  isActive: boolean;
  ipAddress?: string;
  userAgent?: string;
  createdAt: Date;
  lastUsedAt: Date;
}

// ============ PREFERENCE MANAGEMENT TYPES ============

export interface NotificationPreferenceGroup {
  category: string;
  label: string;
  description?: string;
  preferences: {
    type: string;
    label: string;
    description?: string;
    channels: NotificationChannel[];
    defaultEnabled: boolean;
    userPreferences?: UserNotificationPreferences;
  }[];
}

export interface PreferenceUpdateRequest {
  preferences: Array<{
    notificationType: string;
    enabled?: boolean;
    channels?: {
      email?: boolean;
      sms?: boolean;
      push?: boolean;
      inApp?: boolean;
      webhook?: boolean;
    };
    quietHours?: {
      enabled: boolean;
      start?: string;
      end?: string;
    };
    priority?: NotificationPriority;
  }>;
}

// ============ SUBSCRIPTION MANAGEMENT TYPES ============

export interface UserSubscription {
  userId: string;
  appId: string;
  channels: NotificationChannel[];
  preferences: UserNotificationPreferences[];
  globalOptOut: boolean;
  createdAt: Date;
  updatedAt: Date;
  
  // Computed fields
  isActive: boolean;
  lastNotificationAt?: Date;
  totalNotifications: number;
  unreadNotifications: number;
}

export interface SubscriptionUpdate {
  channels?: NotificationChannel[];
  globalOptOut?: boolean;
  preferences?: Partial<CreateUserNotificationPreferences>[];
}

export interface SubscriptionStats {
  totalSubscribers: number;
  activeSubscribers: number;
  subscriptionsByApp: Record<string, {
    total: number;
    active: number;
    optedOut: number;
  }>;
  channelPreferences: Record<NotificationChannel, {
    enabled: number;
    disabled: number;
    percentage: number;
  }>;
  recentSubscriptions: Array<{
    date: string;
    subscriptions: number;
    unsubscriptions: number;
    netChange: number;
  }>;
}

// ============ USER ANALYTICS TYPES ============

export interface UserEngagementMetrics {
  userId: string;
  period: {
    start: Date;
    end: Date;
  };
  notifications: {
    received: number;
    opened: number;
    clicked: number;
    openRate: number;
    clickRate: number;
  };
  channels: Record<NotificationChannel, {
    received: number;
    opened: number;
    openRate: number;
  }>;
  preferences: {
    totalTypes: number;
    enabledTypes: number;
    enabledChannels: NotificationChannel[];
    quietHoursEnabled: boolean;
  };
  activity: {
    lastLogin: Date;
    lastNotificationRead: Date;
    averageReadTime: number; // in minutes
  };
}

export interface UserCohortAnalysis {
  cohortPeriod: 'daily' | 'weekly' | 'monthly';
  cohortDate: Date;
  users: {
    total: number;
    retained: Array<{
      period: number;
      users: number;
      percentage: number;
    }>;
  };
  notifications: {
    averagePerUser: number;
    openRates: Array<{
      period: number;
      rate: number;
    }>;
  };
}

// ============ PRIVACY & COMPLIANCE TYPES ============

export interface DataExportRequest {
  userId: string;
  requestedBy: string;
  requestDate: Date;
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  downloadUrl?: string;
  expiresAt?: Date;
  includeNotifications: boolean;
  includePreferences: boolean;
  includeActivity: boolean;
}

export interface DataDeletionRequest {
  userId: string;
  requestedBy: string;
  requestDate: Date;
  scheduledDate: Date;
  status: 'PENDING' | 'SCHEDULED' | 'PROCESSING' | 'COMPLETED' | 'CANCELLED';
  retentionPeriod: number; // days
  hardDelete: boolean;
  reason?: string;
}

export interface ConsentRecord {
  id: string;
  userId: string;
  consentType: 'EMAIL_MARKETING' | 'SMS_MARKETING' | 'PUSH_NOTIFICATIONS' | 'DATA_PROCESSING' | 'ANALYTICS';
  granted: boolean;
  grantedAt?: Date;
  revokedAt?: Date;
  ipAddress?: string;
  userAgent?: string;
  source: 'WEB' | 'MOBILE' | 'API' | 'ADMIN';
  version: string; // Privacy policy version
  createdAt: Date;
  updatedAt: Date;
}

// ============ NOTIFICATION DELIVERY OPTIMIZATION ============

export interface DeliveryTimeOptimization {
  userId: string;
  timezone: string;
  optimalTimes: {
    email: Array<{
      hour: number;
      minute: number;
      dayOfWeek: number;
      score: number;
    }>;
    sms: Array<{
      hour: number;
      minute: number;
      dayOfWeek: number;
      score: number;
    }>;
    push: Array<{
      hour: number;
      minute: number;
      dayOfWeek: number;
      score: number;
    }>;
  };
  basedOnActivity: {
    totalDataPoints: number;
    lastUpdated: Date;
    confidence: number; // 0-1
  };
}

export interface UserBehaviorPattern {
  userId: string;
  patterns: {
    preferredChannels: Array<{
      channel: NotificationChannel;
      score: number;
      reason: string;
    }>;
    responseTime: {
      average: number; // minutes
      byChannel: Record<NotificationChannel, number>;
      byType: Record<NotificationType, number>;
      byTimeOfDay: Record<number, number>; // hour -> minutes
    };
    engagement: {
      openRate: number;
      clickRate: number;
      unsubscribeRate: number;
      complaintsRate: number;
    };
    quietPeriods: Array<{
      start: { hour: number; minute: number };
      end: { hour: number; minute: number };
      dayOfWeek?: number;
      reason: 'USER_PREFERENCE' | 'LOW_ENGAGEMENT' | 'AUTO_DETECTED';
    }>;
  };
  lastAnalyzed: Date;
  dataPoints: number;
}

// ============ SEGMENTATION & TARGETING ============

export interface UserSegment {
  id: string;
  name: string;
  description?: string;
  conditions: {
    demographics?: {
      countries?: string[];
      languages?: string[];
      timezones?: string[];
      registrationDateRange?: {
        start: Date;
        end: Date;
      };
    };
    behavior?: {
      engagementLevel?: 'HIGH' | 'MEDIUM' | 'LOW';
      lastActiveRange?: {
        start: Date;
        end: Date;
      };
      notificationFrequency?: {
        min: number;
        max: number;
        period: 'DAY' | 'WEEK' | 'MONTH';
      };
    };
    preferences?: {
      enabledChannels?: NotificationChannel[];
      notificationTypes?: NotificationType[];
      hasQuietHours?: boolean;
    };
    subscriptions?: {
      appIds?: string[];
      subscriptionStatus?: 'ACTIVE' | 'INACTIVE' | 'OPTED_OUT';
    };
  };
  userCount?: number;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

export interface SegmentAnalysis {
  segmentId: string;
  analysis: {
    userCount: number;
    demographics: {
      topCountries: Array<{ country: string; count: number }>;
      topLanguages: Array<{ language: string; count: number }>;
      registrationTrends: Array<{ month: string; count: number }>;
    };
    engagement: {
      averageOpenRate: number;
      averageClickRate: number;
      mostEngagedChannels: Array<{ channel: NotificationChannel; rate: number }>;
      optimalSendTimes: Array<{ hour: number; dayOfWeek: number; score: number }>;
    };
    preferences: {
      channelDistribution: Record<NotificationChannel, number>;
      typeDistribution: Record<NotificationType, number>;
      quietHoursUsage: number;
    };
  };
  generatedAt: Date;
}

// ============ A/B TESTING & EXPERIMENTATION ============

export interface NotificationExperiment {
  id: string;
  name: string;
  description?: string;
  status: 'DRAFT' | 'RUNNING' | 'PAUSED' | 'COMPLETED' | 'CANCELLED';
  type: 'CONTENT' | 'TIMING' | 'CHANNEL' | 'FREQUENCY';
  
  targeting: {
    segmentIds?: string[];
    percentage: number; // 0-100
    maxUsers?: number;
  };
  
  variants: Array<{
    id: string;
    name: string;
    weight: number; // 0-100
    config: {
      template?: string;
      timing?: {
        delay: number; // minutes
        optimal: boolean;
      };
      channels?: NotificationChannel[];
      content?: {
        title?: string;
        message?: string;
        customizations?: Record<string, any>;
      };
    };
  }>;
  
  metrics: {
    primary: 'OPEN_RATE' | 'CLICK_RATE' | 'CONVERSION_RATE' | 'UNSUBSCRIBE_RATE';
    secondary?: Array<'OPEN_RATE' | 'CLICK_RATE' | 'CONVERSION_RATE' | 'UNSUBSCRIBE_RATE'>;
  };
  
  schedule: {
    startDate: Date;
    endDate?: Date;
    duration?: number; // days
  };
  
  results?: {
    variants: Array<{
      variantId: string;
      users: number;
      metrics: Record<string, number>;
      confidenceLevel: number;
      isWinner: boolean;
    }>;
    significance: number;
    recommendation: string;
  };
  
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

// ============ AUTOMATION & WORKFLOWS ============

export interface NotificationWorkflow {
  id: string;
  name: string;
  description?: string;
  appId: string;
  status: 'ACTIVE' | 'INACTIVE' | 'DRAFT';
  
  triggers: Array<{
    type: 'USER_ACTION' | 'TIME_BASED' | 'EVENT' | 'API_CALL';
    conditions: Record<string, any>;
    delay?: number; // minutes
  }>;
  
  steps: Array<{
    id: string;
    type: 'SEND_NOTIFICATION' | 'WAIT' | 'CONDITION' | 'UPDATE_USER' | 'WEBHOOK';
    config: Record<string, any>;
    nextSteps?: string[];
  }>;
  
  targeting: {
    segmentIds?: string[];
    userIds?: string[];
    conditions?: Record<string, any>;
  };
  
  settings: {
    maxExecutionsPerUser?: number;
    cooldownPeriod?: number; // minutes
    respectQuietHours: boolean;
    respectUserPreferences: boolean;
  };
  
  stats?: {
    totalExecutions: number;
    activeUsers: number;
    completionRate: number;
    lastExecuted?: Date;
  };
  
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  userId: string;
  status: 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  currentStep: string;
  
  context: Record<string, any>;
  
  steps: Array<{
    stepId: string;
    status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'SKIPPED';
    startedAt?: Date;
    completedAt?: Date;
    error?: string;
    output?: Record<string, any>;
  }>;
  
  startedAt: Date;
  completedAt?: Date;
  error?: string;
}

// ============ REPORTING & DASHBOARD TYPES ============

export interface DashboardMetrics {
  period: {
    start: Date;
    end: Date;
    comparison?: {
      start: Date;
      end: Date;
    };
  };
  
  overview: {
    totalNotifications: number;
    totalUsers: number;
    deliveryRate: number;
    openRate: number;
    clickRate: number;
    unsubscribeRate: number;
    
    // Comparison with previous period
    changes?: {
      totalNotifications: number;
      totalUsers: number;
      deliveryRate: number;
      openRate: number;
      clickRate: number;
      unsubscribeRate: number;
    };
  };
  
  channels: Record<NotificationChannel, {
    sent: number;
    delivered: number;
    opened: number;
    clicked: number;
    failed: number;
    rates: {
      delivery: number;
      open: number;
      click: number;
    };
  }>;
  
  applications: Array<{
    appId: string;
    name: string;
    notifications: number;
    users: number;
    performance: {
      deliveryRate: number;
      openRate: number;
      clickRate: number;
    };
  }>;
  
  trends: Array<{
    date: string;
    notifications: number;
    delivered: number;
    opened: number;
    clicked: number;
  }>;
  
  topPerforming: {
    templates: Array<{
      name: string;
      openRate: number;
      clickRate: number;
      volume: number;
    }>;
    timeSlots: Array<{
      hour: number;
      dayOfWeek: number;
      openRate: number;
      volume: number;
    }>;
  };
}

// ============ INTEGRATION & WEBHOOK TYPES ============

export interface IntegrationConfig {
  id: string;
  appId: string;
  type: 'WEBHOOK' | 'ZAPIER' | 'SLACK' | 'DISCORD' | 'TEAMS' | 'CUSTOM';
  name: string;
  status: 'ACTIVE' | 'INACTIVE' | 'ERROR';
  
  config: {
    url?: string;
    authentication?: {
      type: 'NONE' | 'API_KEY' | 'BEARER_TOKEN' | 'OAUTH2';
      credentials?: Record<string, string>;
    };
    events?: string[];
    filters?: Record<string, any>;
    retryPolicy?: {
      maxRetries: number;
      backoffMultiplier: number;
      maxBackoffSeconds: number;
    };
  };
  
  stats?: {
    totalEvents: number;
    successfulDeliveries: number;
    failedDeliveries: number;
    lastDelivery?: Date;
    averageResponseTime: number;
  };
  
  createdAt: Date;
  updatedAt: Date;
}

export interface WebhookEvent {
  id: string;
  event: string;
  data: Record<string, any>;
  timestamp: Date;
  deliveries: Array<{
    integrationId: string;
    status: 'SUCCESS' | 'FAILED' | 'PENDING' | 'RETRYING';
    attempts: number;
    responseCode?: number;
    responseTime?: number;
    error?: string;
    deliveredAt?: Date;
  }>;
}