export interface Notification {
  id: string;
  title: string;
  message: string;
  type: string;
  priority: string;
  recipientId: string;
  senderId?: string;
  appId: string;
  status: string;
  scheduledFor?: Date;
  expiresAt?: Date;
  metadata: string | any;
  novuNotificationId?: string;
  createdAt: Date;
  updatedAt: Date;
  readAt?: Date;
  
  // Relations (when included)
  recipient?: {
    id: string;
    email: string;
    name?: string;
    phone?: string;
  };
  app?: {
    appId: string;
    name: string;
  };
  deliveries?: DeliveryStatus[];
  webhookDeliveries?: WebhookDelivery[];
}

export interface CreateNotification {
  title: string;
  message: string;
  type: string;
  priority?: string;
  recipientId: string;
  senderId?: string;
  appId: string;
  scheduledFor?: Date;
  expiresAt?: Date;
  metadata?: any;
  novuNotificationId?: string;
}

export interface UpdateNotification {
  title?: string;
  message?: string;
  type?: string;
  priority?: string;
  status?: string;
  scheduledFor?: Date;
  expiresAt?: Date;
  metadata?: any;
  readAt?: Date;
}

export interface NotificationFilter {
  page?: number;
  limit?: number;
  appId?: string;
  recipientId?: string;
  status?: string;
  type?: string;
  priority?: string;
  startDate?: string | Date;
  endDate?: string | Date;
  includeRead?: boolean;
}

export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface NotificationStats {
  total: number;
  byStatus: {
    pending: number;
    sent: number;
    delivered: number;
    failed: number;
    read: number;
  };
  byType: Record<string, number>;
  byPriority: Record<string, number>;
}

export interface BulkNotificationResult {
  successful: Notification[];
  failed: Array<{
    notification: CreateNotification;
    error: string;
  }>;
  totalCount: number;
}

// ============ DELIVERY STATUS TYPES ============

export interface DeliveryStatus {
  id: string;
  notificationId: string;
  channel: string; // email, sms, push, in_app, webhook
  provider: string; // novu, resend, twilio, etc.
  status: string; // PENDING, SENT, DELIVERED, FAILED, BOUNCED
  attempts: number;
  deliveredAt?: Date;
  errorMessage?: string;
  errorCode?: string;
  providerMessageId?: string;
  providerResponse: string | any;
  createdAt: Date;
  updatedAt: Date;
}

// ============ USER PREFERENCES TYPES ============

export interface UserNotificationPreferences {
  id: string;
  userId: string;
  notificationType: string;
  enabled: boolean;
  priority: string;
  emailEnabled: boolean;
  smsEnabled: boolean;
  pushEnabled: boolean;
  inAppEnabled: boolean;
  webhookEnabled: boolean;
  quietHoursEnabled: boolean;
  quietHoursStart?: string;
  quietHoursEnd?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateUserNotificationPreferences {
  enabled?: boolean;
  priority?: string;
  emailEnabled?: boolean;
  smsEnabled?: boolean;
  pushEnabled?: boolean;
  inAppEnabled?: boolean;
  webhookEnabled?: boolean;
  quietHoursEnabled?: boolean;
  quietHoursStart?: string;
  quietHoursEnd?: string;
}

// ============ TEMPLATE TYPES ============

export interface NotificationTemplate {
  id: string;
  name: string;
  description?: string;
  title: string;
  message: string;
  type: string;
  priority: string;
  emailSubject?: string;
  emailBody?: string;
  smsMessage?: string;
  pushTitle?: string;
  pushBody?: string;
  channels: string[];
  metadata: string | any;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateNotificationTemplate {
  name: string;
  description?: string;
  title: string;
  message: string;
  type: string;
  priority?: string;
  emailSubject?: string;
  emailBody?: string;
  smsMessage?: string;
  pushTitle?: string;
  pushBody?: string;
  channels?: string[];
  metadata?: any;
}

export interface UpdateNotificationTemplate {
  description?: string;
  title?: string;
  message?: string;
  type?: string;
  priority?: string;
  emailSubject?: string;
  emailBody?: string;
  smsMessage?: string;
  pushTitle?: string;
  pushBody?: string;
  channels?: string[];
  metadata?: any;
}

// ============ WEBHOOK TYPES ============

export interface WebhookEndpoint {
  id: string;
  name: string;
  url: string;
  secret: string;
  events: string[];
  enabled: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateWebhookEndpoint {
  name: string;
  url: string;
  secret?: string;
  events?: string[];
  enabled?: boolean;
}

export interface WebhookDelivery {
  id: string;
  webhookId: string;
  notificationId: string;
  status: string; // SUCCESS, FAILED, TIMEOUT
  responseCode?: number;
  responseBody?: string;
  errorMessage?: string;
  attempts: number;
  nextRetryAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  
  // Relations (when included)
  webhook?: {
    id: string;
    name: string;
    url: string;
  };
}

// ============ NOTIFICATION CHANNELS ============

export type NotificationChannel = 
  | 'email' 
  | 'sms' 
  | 'push' 
  | 'in_app' 
  | 'webhook'
  | 'slack'
  | 'discord'
  | 'teams';

export type NotificationStatus = 
  | 'PENDING' 
  | 'SENT' 
  | 'DELIVERED' 
  | 'READ' 
  | 'FAILED'
  | 'EXPIRED'
  | 'CANCELLED';

export type NotificationType = 
  | 'INFO' 
  | 'SUCCESS' 
  | 'WARNING' 
  | 'ERROR'
  | 'REMINDER'
  | 'ALERT'
  | 'MARKETING'
  | 'SYSTEM';

export type NotificationPriority = 
  | 'LOW' 
  | 'NORMAL' 
  | 'HIGH' 
  | 'URGENT'
  | 'CRITICAL';

export type DeliveryStatusType = 
  | 'PENDING' 
  | 'SENT' 
  | 'DELIVERED' 
  | 'FAILED' 
  | 'BOUNCED'
  | 'BLOCKED'
  | 'REJECTED';

// ============ PROVIDER INTEGRATION TYPES ============

export interface NotificationProvider {
  name: string;
  type: 'primary' | 'fallback';
  channels: NotificationChannel[];
  config: Record<string, any>;
}

export interface SendNotificationRequest {
  notification: CreateNotification;
  channels: NotificationChannel[];
  template?: string;
  providerOverride?: string;
  retryConfig?: {
    maxRetries?: number;
    retryDelay?: number;
    fallbackStrategy?: 'failover' | 'parallel' | 'hybrid';
  };
}

export interface SendNotificationResponse {
  success: boolean;
  notificationId: string;
  deliveries: Array<{
    channel: NotificationChannel;
    provider: string;
    status: DeliveryStatusType;
    providerMessageId?: string;
    errorMessage?: string;
  }>;
  errors?: Array<{
    channel: NotificationChannel;
    provider: string;
    error: string;
  }>;
}

// ============ NOVU INTEGRATION TYPES ============

export interface NovuSubscriberData {
  subscriberId: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  avatar?: string;
  locale?: string;
  data?: Record<string, any>;
}

export interface NovuNotificationPayload {
  name: string; // workflow/template identifier
  to: NovuSubscriberData | string;
  payload: Record<string, any>;
  overrides?: {
    email?: {
      from?: string;
      subject?: string;
    };
    sms?: {
      from?: string;
    };
    push?: {
      title?: string;
      body?: string;
    };
  };
  actor?: NovuSubscriberData | string;
  tenant?: string;
}

export interface NovuResponse {
  data: {
    transactionId: string;
    acknowledged: boolean;
    status: string;
  };
}

// ============ RESEND INTEGRATION TYPES ============

export interface ResendEmailPayload {
  from: string;
  to: string | string[];
  subject: string;
  html?: string;
  text?: string;
  cc?: string | string[];
  bcc?: string | string[];
  reply_to?: string | string[];
  tags?: Array<{
    name: string;
    value: string;
  }>;
  headers?: Record<string, string>;
  attachments?: Array<{
    filename: string;
    content: string | Buffer;
    contentType?: string;
  }>;
}

export interface ResendResponse {
  id: string;
  from: string;
  to: string[];
  created_at: string;
}

// ============ TWILIO INTEGRATION TYPES ============

export interface TwilioSmsPayload {
  to: string;
  from: string;
  body: string;
  mediaUrl?: string[];
  statusCallback?: string;
  maxPrice?: string;
  provideFeedback?: boolean;
  attempt?: number;
  validityPeriod?: number;
}

export interface TwilioResponse {
  sid: string;
  status: string;
  to: string;
  from: string;
  body: string;
  date_created: string;
  date_updated: string;
  error_code?: string;
  error_message?: string;
}

// ============ BATCH PROCESSING TYPES ============

export interface BatchNotificationJob {
  id: string;
  appId: string;
  notifications: CreateNotification[];
  priority: NotificationPriority;
  scheduledFor?: Date;
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  progress: {
    total: number;
    processed: number;
    successful: number;
    failed: number;
  };
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}

export interface CreateBatchNotificationJob {
  appId: string;
  notifications: CreateNotification[];
  priority?: NotificationPriority;
  scheduledFor?: Date;
}

// ============ ANALYTICS & METRICS TYPES ============

export interface NotificationMetrics {
  timeRange: {
    start: Date;
    end: Date;
  };
  appId?: string;
  totals: {
    sent: number;
    delivered: number;
    failed: number;
    read: number;
    bounced: number;
  };
  byChannel: Record<NotificationChannel, {
    sent: number;
    delivered: number;
    failed: number;
    deliveryRate: number;
  }>;
  byProvider: Record<string, {
    sent: number;
    delivered: number;
    failed: number;
    avgDeliveryTime: number;
    reliability: number;
  }>;
  byType: Record<NotificationType, {
    sent: number;
    readRate: number;
    avgTimeToRead: number;
  }>;
  trends: Array<{
    date: string;
    sent: number;
    delivered: number;
    failed: number;
  }>;
}

// ============ ERROR HANDLING TYPES ============

export interface NotificationError {
  code: string;
  message: string;
  details?: any;
  retryable: boolean;
  channel?: NotificationChannel;
  provider?: string;
}

export interface RetryConfig {
  maxRetries: number;
  retryDelay: number;
  backoffMultiplier: number;
  maxRetryDelay: number;
  retryableErrors: string[];
}

// ============ QUEUE & SCHEDULING TYPES ============

export interface ScheduledNotification extends CreateNotification {
  scheduledFor: Date;
  timezone?: string;
  recurring?: {
    pattern: 'daily' | 'weekly' | 'monthly' | 'yearly' | 'custom';
    interval: number;
    endDate?: Date;
    maxOccurrences?: number;
    customCron?: string;
  };
}

export interface QueuedNotification {
  id: string;
  notification: CreateNotification;
  channels: NotificationChannel[];
  priority: NotificationPriority;
  retryCount: number;
  maxRetries: number;
  nextRetryAt?: Date;
  queuedAt: Date;
  processingStartedAt?: Date;
  status: 'QUEUED' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
}

// ============ SUBSCRIPTION & USER MANAGEMENT TYPES ============

export interface UserSubscription {
  userId: string;
  appId: string;
  channels: NotificationChannel[];
  preferences: UserNotificationPreferences[];
  globalOptOut: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface SubscriptionUpdate {
  channels?: NotificationChannel[];
  globalOptOut?: boolean;
  preferences?: Partial<CreateUserNotificationPreferences>[];
}

// ============ AUDIT & COMPLIANCE TYPES ============

export interface NotificationAuditLog {
  id: string;
  notificationId: string;
  action: 'CREATED' | 'SENT' | 'DELIVERED' | 'READ' | 'FAILED' | 'CANCELLED' | 'UPDATED';
  actorId?: string;
  actorType: 'USER' | 'SYSTEM' | 'APPLICATION';
  details: Record<string, any>;
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
}

export interface ComplianceReport {
  appId: string;
  period: {
    start: Date;
    end: Date;
  };
  metrics: {
    totalNotifications: number;
    optOuts: number;
    bounces: number;
    complaints: number;
    unsubscribeRate: number;
  };
  violations?: Array<{
    type: string;
    description: string;
    count: number;
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  }>;
  recommendations?: string[];
}

// ============ TEMPLATE VARIABLE TYPES ============

export interface TemplateVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object';
  required: boolean;
  defaultValue?: any;
  description?: string;
  validation?: {
    pattern?: string;
    min?: number;
    max?: number;
    options?: any[];
  };
}

export interface CompiledTemplate {
  title: string;
  message: string;
  channels: {
    email?: {
      subject: string;
      html: string;
      text?: string;
    };
    sms?: {
      message: string;
    };
    push?: {
      title: string;
      body: string;
    };
    inApp?: {
      title: string;
      message: string;
      actionUrl?: string;
    };
  };
}

// ============ API RESPONSE TYPES ============

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  meta?: {
    timestamp: string;
    requestId: string;
    version: string;
  };
}